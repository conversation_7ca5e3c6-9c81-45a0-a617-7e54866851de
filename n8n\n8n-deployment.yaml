apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    service: n8n
  name: n8n
  namespace: tenk8n
spec:
  replicas: 1
  selector:
    matchLabels:
      service: n8n
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        service: n8n
    spec:
      initContainers:
        - name: volume-permissions
          image: busybox:1.36
          command: ["sh", "-c", "chown 1000:1000 /data"]
          volumeMounts:
            - name: n8n-claim0
              mountPath: /data
      containers:
        - command:
            - /bin/sh
          args:
            - -c
            - sleep 5; n8n start
          env:
            - name: DB_TYPE
              value: postgresdb
            - name: DB_POSTGRESDB_HOST
              value: postgres-service
            - name: DB_POSTGRESDB_PORT
              value: "5432"
            - name: DB_POSTGRESDB_DATABASE
              value: n8n
            - name: DB_POSTGRESDB_USER
              valueFrom:
                secretKeyRef:
                  name: postgres-secret
                  key: POSTGRES_NON_ROOT_USER
            - name: DB_POSTGRESDB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: postgres-secret
                  key: POSTGRES_NON_ROOT_PASSWORD
            - name: DB_POSTGRESDB_CONNECTION_TIMEOUT
              value: "60000"
            - name: N8N_PROTOCOL
              value: https
            - name: N8N_PORT
              value: "5678"
            - name: N8N_WEBHOOK
              value: "https://n8n.tenksolutions.com"
          image: n8nio/n8n
          name: n8n
          ports:
            - containerPort: 5678
          resources:
            requests:
              memory: "250Mi"
            limits:
              memory: "400Mi"
          volumeMounts:
            - mountPath: /home/<USER>/.n8n
              name: n8n-claim0
      restartPolicy: Always
      volumes:
        - name: n8n-claim0
          persistentVolumeClaim:
            claimName: n8n-claim0
        - name: n8n-secret
          secret:
            secretName: n8n-secret
        - name: postgres-secret
          secret:
            secretName: postgres-secret

