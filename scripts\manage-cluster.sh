#!/bin/bash
set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Configuration
# source <(cat .env | xargs -I {} sed 's/=/ /' | xargs -n 2 sh -c 'export $1="$2"')
PROJECT_ID=$(gcloud config get-value project)
REGION=$(gcloud config get-value compute/region)
CLUSTER_NAME="tenk8n"
NAMESPACE="tenk8n"

function print_header() {
  echo -e "\n${GREEN}==== $1 ====${NC}\n"
}

# Create namespace if it doesn't exist
function create_namespace() {
  print_header "Creating namespace $NAMESPACE if it doesn't exist"
  kubectl get namespace $NAMESPACE > /dev/null 2>&1 || kubectl create namespace $NAMESPACE
}

# Apply all Kubernetes configurations
function apply_all() {
  print_header "Applying all Kubernetes configurations"
  
  # Apply storage class first
  kubectl apply -f storage.yaml
  
  # Apply PostgreSQL resources
  kubectl apply -f postgres/postgres-configmap.yaml
  kubectl apply -f postgres/postgres-secret.yaml
  kubectl apply -f postgres/postgres-deployment.yaml
  kubectl apply -f postgres/postgres-service.yaml
  
  # Apply n8n resources
  kubectl apply -f n8n/n8n-claim0-persistentvolumeclaim.yaml
  kubectl apply -f n8n/n8n-deployment.yaml
  kubectl apply -f n8n/n8n-service.yaml
  
  # Apply Qdrant resources
  kubectl apply -f qdrant/qdrant-config.yaml
  kubectl apply -f qdrant/qdrant-secret.yaml
  kubectl apply -f qdrant/qdrant-pvc.yaml
  kubectl apply -f qdrant/qdrant-deployment.yaml
  kubectl apply -f qdrant/qdrant-service.yaml
  kubectl apply -f qdrant/qdrant-pdb.yaml
}

# Create a new GKE cluster
function create_cluster() {
  print_header "Creating GKE cluster $CLUSTER_NAME"
  
  gcloud container clusters create $CLUSTER_NAME \
    --region $REGION \
    --num-nodes 1 \
    --machine-type e2-standard-4 \
    --disk-size 100 \
    --enable-autoscaling \
    --min-nodes 1 \
    --max-nodes 3
    
  # Connect to the cluster
  gcloud container clusters get-credentials $CLUSTER_NAME --region $REGION --project $PROJECT_ID
  
  # Create namespace
  create_namespace
}

# Delete the GKE cluster
function delete_cluster() {
  print_header "WARNING: This will delete the entire cluster $CLUSTER_NAME"
  read -p "Are you sure you want to continue? (y/n): " confirm
  
  if [ "$confirm" != "y" ]; then
    echo "Aborted."
    return
  fi
  
  gcloud container clusters delete $CLUSTER_NAME --region $REGION --quiet
}

# Scale the cluster
function scale_cluster() {
  print_header "Scaling cluster node pool"
  read -p "Enter number of nodes: " node_count
  
  gcloud container clusters resize $CLUSTER_NAME --region $REGION --num-nodes $node_count --quiet
}

# Show cluster info
function show_cluster_info() {
  print_header "Cluster Information"
  gcloud container clusters describe $CLUSTER_NAME --region $REGION
  
  print_header "Node Information"
  kubectl get nodes
  
  print_header "Pod Information"
  kubectl get pods -n $NAMESPACE
  
  print_header "Service Information"
  kubectl get services -n $NAMESPACE
  
  print_header "PVC Information"
  kubectl get pvc -n $NAMESPACE
}

# Main menu
function show_menu() {
  echo -e "${GREEN}=== Kubernetes Cluster Management ===${NC}"
  echo "1) Create new GKE cluster"
  echo "2) Delete GKE cluster"
  echo "3) Scale cluster"
  echo "4) Apply all Kubernetes configurations"
  echo "5) Show cluster information"
  echo "q) Quit"
  echo ""
  read -p "Select an option: " option
  
  case $option in
    1) create_cluster ;;
    2) delete_cluster ;;
    3) scale_cluster ;;
    4) apply_all ;;
    5) show_cluster_info ;;
    q) exit 0 ;;
    *) echo -e "${RED}Invalid option" ;;
  esac
  
  read -p "Press Enter to continue..."
  show_menu
}

# Start the menu
show_menu