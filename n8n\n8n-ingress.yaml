apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: n8n-ingress
  namespace: tenk8n
  annotations:
    kubernetes.io/ingress.global-static-ip-name: "tenk8n-static-ip"
    networking.gke.io/managed-certificates: "tenk8n-managed-cert"
    kubernetes.io/ingress.class: "gce"
spec:
  rules:
  - host: n8n.tenksolutions.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: n8n
            port:
              number: 5678