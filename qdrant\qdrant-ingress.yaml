apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: qdrant-ingress
  namespace: tenk8n
  annotations:
    kubernetes.io/ingress.global-static-ip-name: "tenk8n-qdrant-ip"
    networking.gke.io/managed-certificates: "tenk8n-qdrant-cert"
    kubernetes.io/ingress.class: "gce"
    kubernetes.io/ingress.allow-http: "true"
    networking.gke.io/v1beta1.FrontendConfig: "qdrant-frontend-config"
spec:
  rules:
  - host: qdrant.tenksolutions.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: qdrant
            port:
              number: 6333