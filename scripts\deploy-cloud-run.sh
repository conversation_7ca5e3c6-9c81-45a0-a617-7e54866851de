#!/bin/bash
set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Configuration
## Read env variables
# source <(cat .env | xargs -I {} sed 's/=/ /' | xargs -n 2 sh -c 'export $1="$2"')
PROJECT_ID=$(gcloud config get-value project)
REGION=$(gcloud config get-value compute/region)
IMAGE_NAME="tenk8n"

# Check if required environment variables are set
# if [ -z "$GITHUB_ACCESS_TOKEN" ]; then
#   echo -e "${RED}Error: GITHUB_ACCESS_TOKEN environment variable is not set${NC}"
#   exit 1
# fi

# if [ -z "$CODE_SANDBOX_ACCESS_TOKEN" ]; then
#   echo -e "${RED}Error: CODE_SANDBOX_ACCESS_TOKEN environment variable is not set${NC}"
#   exit 1
# fi

# if [ -z "$QDRANT_HOST" ]; then
#   echo -e "${RED}Error: QDRANT_HOST environment variable is not set${NC}"
#   exit 1
# fi

# if [ -z "$QDRANT_PORT" ]; then
#   echo -e "${RED}Error: QDRANT_PORT environment variable is not set${NC}"
#   exit 1
# fi

# Build Docker image
echo -e "${GREEN}Building Docker image...${NC}"
docker build -t gcr.io/$PROJECT_ID/$IMAGE_NAME:latest .

# Push Docker image
echo -e "${GREEN}Pushing Docker image to Google Container Registry...${NC}"
docker push gcr.io/$PROJECT_ID/$IMAGE_NAME:latest

# Deploy to Cloud Run
echo -e "${GREEN}Deploying to Cloud Run...${NC}"
gcloud run deploy $IMAGE_NAME \
  --image gcr.io/$PROJECT_ID/$IMAGE_NAME:latest \
  --platform managed \
  --region $REGION \
  --allow-unauthenticated \
  # --set-env-vars GITHUB_ACCESS_TOKEN=$GITHUB_ACCESS_TOKEN,CODE_SANDBOX_ACCESS_TOKEN=$CODE_SANDBOX_ACCESS_TOKEN,QDRANT_HOST=$QDRANT_HOST,QDRANT_PORT=$QDRANT_PORT

echo -e "${GREEN}Deployment completed successfully!${NC}"