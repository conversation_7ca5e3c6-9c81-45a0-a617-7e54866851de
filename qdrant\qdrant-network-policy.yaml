apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: qdrant-network-policy
  namespace: tenk8n
spec:
  podSelector:
    matchLabels:
      app: qdrant
  policyTypes:
  - Ingress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          service: n8n
    ports:
    - protocol: TCP
      port: 6333
    - protocol: TCP
      port: 6334
  - from:
    - namespaceSelector: {}
      podSelector:
        matchLabels:
          app: qdrant-metrics
    ports:
    - protocol: TCP
      port: 6333
  - ports:
    - protocol: TCP
      port: 6333
    - protocol: TCP
      port: 6334