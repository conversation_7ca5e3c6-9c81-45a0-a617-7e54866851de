const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const moment = require('moment');
require('dotenv').config();

// Configuration
const NAMESPACE = 'tenk8n';
const BACKUP_DIR = path.join(__dirname, '../backups');
const RETENTION_DAYS = 30; // Keep backups for 30 days

// Ensure backup directory exists
if (!fs.existsSync(BACKUP_DIR)) {
  fs.mkdirSync(BACKUP_DIR, { recursive: true });
}

// Get PostgreSQL pod name
function getPostgresPodName() {
  try {
    return execSync(
      `kubectl get pods -n ${NAMESPACE} -l service=postgres-n8n -o jsonpath="{.items[0].metadata.name}"`,
      { encoding: 'utf8' }
    ).trim();
  } catch (error) {
    console.error('Error getting PostgreSQL pod name:', error.message);
    process.exit(1);
  }
}

// Create backup
function createBackup() {
  const timestamp = moment().format('YYYYMMDD_HHmmss');
  const backupFile = path.join(BACKUP_DIR, `n8n_backup_${timestamp}.sql`);
  const podName = getPostgresPodName();
  
  console.log(`Creating backup using pod: ${podName}`);
  
  try {
    execSync(
      `kubectl exec -n ${NAMESPACE} ${podName} -- pg_dump -U tenk8nDB n8n > ${backupFile}`,
      { stdio: 'inherit' }
    );
    
    console.log(`Backup saved to: ${backupFile}`);
    
    // Compress the backup
    execSync(`gzip ${backupFile}`, { stdio: 'inherit' });
    console.log(`Backup compressed: ${backupFile}.gz`);
    
    return `${backupFile}.gz`;
  } catch (error) {
    console.error('Error creating backup:', error.message);
    process.exit(1);
  }
}

// Clean up old backups
function cleanupOldBackups() {
  console.log('Cleaning up old backups...');
  
  const files = fs.readdirSync(BACKUP_DIR);
  const now = moment();
  
  files.forEach(file => {
    if (!file.startsWith('n8n_backup_')) return;
    
    const filePath = path.join(BACKUP_DIR, file);
    const stats = fs.statSync(filePath);
    const fileDate = moment(stats.mtime);
    const daysDiff = now.diff(fileDate, 'days');
    
    if (daysDiff > RETENTION_DAYS) {
      console.log(`Removing old backup: ${file} (${daysDiff} days old)`);
      fs.unlinkSync(filePath);
    }
  });
}

// Optional: Upload to Google Cloud Storage
function uploadToGCS(backupFile) {
  if (!process.env.GCS_BUCKET) {
    console.log('GCS_BUCKET not defined, skipping upload');
    return;
  }
  
  try {
    console.log(`Uploading to GCS bucket: ${process.env.GCS_BUCKET}`);
    execSync(
      `gsutil cp ${backupFile} gs://${process.env.GCS_BUCKET}/backups/`,
      { stdio: 'inherit' }
    );
    console.log('Upload complete');
  } catch (error) {
    console.error('Error uploading to GCS:', error.message);
  }
}

// Main execution
console.log('Starting PostgreSQL backup process...');
const backupFile = createBackup();
cleanupOldBackups();
uploadToGCS(backupFile);
console.log('Backup process completed');