# AI Dev Container - Developer Sandbox Repository

This project sets up The Handsomest Nerd, Inc.'s custom n8n environment with integrated Qdrant support, deployed via GitHub Actions to Google Cloud Run or Kubernetes.

## Project Structure

```mermaid
ai-dev-sandbox/
│ ├── firebase-app                 # Firebase app (User-facing and Admin UI)
│ │ ├── package.json                # Node.js package configuration
│ │ ├── tsconfig.json               # TypeScript configuration
│ │ ├── .firebaserc                 # Firebase project configuration
│ │ ├── firebase.json               # Firebase hosting configuration
│ │ ├── .gitignore                  # Git ignore rules
│ │ ├── .env.example                # Environment variables template
│ │ ├── src/                        # Source code directory
│ │ │ ├── App.tsx                   # Main React application component
│ │ │ └── index.tsx                 # Application entry point
│ │ ├── /components                 # React components directory
│ │ │ ├── VendingMachine.tsx        # Vending machine interface component
│ │ │ ├── WorkflowOrchestrator.tsx  # Workflow Orchestrator interface component
│ │ │ └── PasswordlessAuth.tsx      # Authentication component
│ │ ├── /public                     # Public assets directory
│ │ │ └── index.html                # HTML entry point
│ │ ├── /functions                  # Firebase Cloud Functions directory
│ │ │ └── package.json              # Cloud Functions dependencies
├── /n8n-workflows                  # n8n automation directory
│ ├── /workflows                    # n8n workflow definitions
│ └── /n8n-config                   # n8n environment configuration
├── /thn-n8n-container              # Custom n8n container directory
│ ├── Dockerfile                    # Container build configuration
│ ├── .dockerignore                 # Docker ignore rules
│ └── /entrypoint                   # Container startup scripts
├── /shared-workspace              # AI Developer Sandbox
├── /code-server                    # Browser-based IDE directory (not implemented)
├── /dockerfile                     # IDE container configuration (not implemented)
├── /config                         # IDE settings and integrations (not implemented)
├── /scripts                        # Maintenance and deployment scripts
│ ├── maintenance.sh                # Interactive maintenance script
│ ├── manage-cluster.sh             # Kubernetes cluster management
│ ├── deploy-cloud-run.sh           # Cloud Run deployment script
│ └── backup-postgres.js            # Database backup script
├── /n8n                            # n8n Kubernetes configuration
│ ├── n8n-deployment.yaml           # n8n deployment configuration
│ ├── n8n-service.yaml              # n8n service configuration
│ └── n8n-ingress.yaml              # n8n ingress configuration
├── /postgres                       # PostgreSQL Kubernetes configuration
│ ├── postgres-deployment.yaml      # PostgreSQL deployment configuration
│ ├── postgres-service.yaml         # PostgreSQL service configuration
│ └── postgres-secret.yaml          # PostgreSQL secrets
├── /qdrant                         # Qdrant Kubernetes configuration
│ ├── qdrant-deployment.yaml        # Qdrant deployment configuration
│ ├── qdrant-service.yaml           # Qdrant service configuration
│ └── qdrant-config.yaml            # Qdrant configuration
└── README.md                       # Project documentation
```

## Features

- Custom Docker image with `curl`, `bash`, `nodejs`, and `firebase` installed
- Environment variables for GitHub and CodeSandbox tokens
- Automated deployment using GitHub Actions
- Qdrant integration for vector search capabilities
- Kubernetes deployment with n8n, PostgreSQL, and Qdrant
- Cloud Run deployment option
- Maintenance scripts for common operations

## Setup

### Prerequisites

- Google Cloud SDK (`gcloud`)
- Kubernetes CLI (`kubectl`)
- Docker
- Node.js and npm

### Initial Setup

1. Clone the repository:
   ```bash
   git clone https://github.com/your-org/ai-dev-sandbox.git
   cd ai-dev-sandbox
   ```

2. Create a `.env` file with the necessary environment variables:
   ```
   GITHUB_ACCESS_TOKEN=your_github_token
   CODE_SANDBOX_ACCESS_TOKEN=your_codesandbox_token
   QDRANT_HOST=your_qdrant_host
   QDRANT_PORT=6333
   GCS_BUCKET=your_gcs_bucket_name
   ```

3. Install dependencies:
   ```bash
   npm install
   ```

4. Set up Google Cloud project:
   ```bash
   gcloud config set project your-project-id
   gcloud config set compute/region us-central1
   ```

## Maintenance Commands

### Using npm Scripts

The project includes npm scripts for common operations:

```bash
# Interactive maintenance menu
npm run maintenance

# Connect to the Kubernetes cluster
npm run connect

# Check pod status
npm run check:pods

# Check persistent volumes
npm run check:volumes

# Restart services
npm run restart:n8n
npm run restart:postgres
npm run restart:qdrant

# View logs
npm run logs:n8n
npm run logs:postgres
npm run logs:qdrant

# Backup PostgreSQL database
npm run backup:postgres

# Check Qdrant status
npm run check:qdrant
```

### Using Bash Scripts Directly

You can also run the bash scripts directly:

```bash
# Interactive maintenance menu
bash scripts/maintenance.sh

# Kubernetes cluster management
bash scripts/manage-cluster.sh

# Deploy to Cloud Run
bash scripts/deploy-cloud-run.sh
```

## Common Operations

### Checking System Status

To check the status of all components:

```bash
# Check pod status
npm run check:pods

# Check persistent volumes
npm run check:volumes

# Check Qdrant status
npm run check:qdrant
```

### Redeploying After Configuration Changes

After updating YAML files, you need to apply the changes to the cluster:

1. Connect to the cluster:
   ```bash
   npm run connect
   ```

2. Apply the updated configuration:
   ```bash
   # For n8n changes
   kubectl apply -f n8n/n8n-deployment.yaml -n tenk8n
   
   # For PostgreSQL changes
   kubectl apply -f postgres/postgres-deployment.yaml -n tenk8n
   
   # For Qdrant changes
   kubectl apply -f qdrant/qdrant-deployment.yaml -n tenk8n
   
   # Or apply all configurations at once
   kubectl apply -f n8n/ -f postgres/ -f qdrant/ -n tenk8n
   ```

3. Restart the affected deployment:
   ```bash
   npm run restart:n8n
   # or
   npm run restart:postgres
   # or
   npm run restart:qdrant
   ```

### Backing Up the Database

To create a backup of the PostgreSQL database:

```bash
npm run backup:postgres
```

This will:
1. Create a backup file in the `backups/` directory
2. Compress the backup file
3. Clean up backups older than 30 days
4. Upload the backup to Google Cloud Storage (if GCS_BUCKET is set)

### Viewing Logs

To view logs for a specific service:

```bash
npm run logs:n8n
# or
npm run logs:postgres
# or
npm run logs:qdrant
```

### Deploying to Cloud Run

To deploy the application to Cloud Run:

1. Ensure environment variables are set:
   ```bash
   export GITHUB_ACCESS_TOKEN=your_github_token
   export CODE_SANDBOX_ACCESS_TOKEN=your_codesandbox_token
   export QDRANT_HOST=your_qdrant_host
   export QDRANT_PORT=6333
   ```

2. Run the deployment script:
   ```bash
   bash scripts/deploy-cloud-run.sh
   ```

### Managing the Kubernetes Cluster

For cluster management operations, use the interactive menu:

```bash
bash scripts/manage-cluster.sh
```

This provides options to:
- Create a new GKE cluster
- Delete the GKE cluster
- Scale the cluster
- Apply all Kubernetes configurations
- Show cluster information

## Troubleshooting

### Common Issues

1. **Pods in CrashLoopBackOff state**:
   ```bash
   npm run logs:n8n  # Replace with the affected service
   ```
   Check the logs to identify the issue.

2. **Database connection issues**:
   Ensure PostgreSQL is running and accessible:
   ```bash
   kubectl exec -n tenk8n $(kubectl get pods -n tenk8n -l service=postgres-n8n -o jsonpath='{.items[0].metadata.name}') -- pg_isready
   ```

3. **Qdrant not responding**:
   Check Qdrant's readiness:
   ```bash
   npm run check:qdrant
   ```

### Restarting Services

If a service is not functioning correctly, try restarting it:

```bash
npm run restart:n8n
# or
npm run restart:postgres
# or
npm run restart:qdrant
```

## Deployment

### GitHub Actions

The project includes a GitHub Actions workflow for automated deployment to Cloud Run. To use it:

1. Add the following secrets to your GitHub repository:
   - `GCP_PROJECT_ID`: Your Google Cloud project ID
   - `GCP_SA_KEY`: Base64-encoded service account key
   - `GCP_REGION`: Google Cloud region
   - `GITHUB_ACCESS_TOKEN`: GitHub access token
   - `CODE_SANDBOX_ACCESS_TOKEN`: CodeSandbox access token
   - `QDRANT_HOST`: Qdrant host
   - `QDRANT_PORT`: Qdrant port

2. Push changes to the `main` branch to trigger deployment.

### Manual Deployment

For manual deployment to Kubernetes:

1. Connect to the cluster:
   ```bash
   npm run connect
   ```

2. Apply all configurations:
   ```bash
   kubectl apply -f namespace.yaml
   kubectl apply -f storage.yaml
   kubectl apply -f postgres/ -f n8n/ -f qdrant/ -n tenk8n
   ```

## Access

- **n8n**: Access via the URL provided by Google Cloud Run or through the Kubernetes ingress at `https://n8n.tenksolutions.com`
- **Qdrant**: Access via the Kubernetes service at `qdrant.tenk8n.svc.cluster.local:6333` within the cluster

## Resources

- [n8n Documentation](https://docs.n8n.io/)
- [Qdrant Documentation](https://qdrant.tech/documentation/)
- [Google Kubernetes Engine Documentation](https://cloud.google.com/kubernetes-engine/docs)
- [Google Cloud Run Documentation](https://cloud.google.com/run/docs)
