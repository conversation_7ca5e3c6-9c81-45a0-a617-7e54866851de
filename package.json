{"name": "tenk8n-maintenance", "version": "1.0.0", "description": "Maintenance scripts for The Handsomest Nerd Kubernetes setup", "scripts": {"maintenance": "bash scripts/maintenance.sh", "connect": "gcloud container clusters get-credentials tenk8n --region $(gcloud config get-value compute/region) --project $(gcloud config get-value project)", "check:pods": "kubectl get pods -n tenk8n", "check:volumes": "kubectl get pv && kubectl get pvc -n tenk8n", "restart:n8n": "kubectl rollout restart deployment/n8n -n tenk8n && kubectl rollout status deployment/n8n -n tenk8n", "restart:postgres": "kubectl rollout restart deployment/postgres -n tenk8n && kubectl rollout status deployment/postgres -n tenk8n", "restart:qdrant": "kubectl rollout restart deployment/qdrant -n tenk8n && kubectl rollout status deployment/qdrant -n tenk8n", "logs:n8n": "kubectl get pods -n tenk8n -l service=n8n --no-headers && kubectl logs -n tenk8n $(kubectl get pods -n tenk8n -l service=n8n -o jsonpath='{.items[0].metadata.name}' 2>/dev/null || echo '') --tail=100 || echo 'No n8n pods found'", "logs:postgres": "kubectl get pods -n tenk8n -l service=postgres-n8n --no-headers && kubectl logs -n tenk8n $(kubectl get pods -n tenk8n -l service=postgres-n8n -o jsonpath='{.items[0].metadata.name}' 2>/dev/null || echo '') --tail=100 || echo 'No postgres pods found'", "logs:qdrant": "kubectl get pods -n tenk8n -l app=qdrant --no-headers && kubectl logs -n tenk8n $(kubectl get pods -n tenk8n -l app=qdrant -o jsonpath='{.items[0].metadata.name}' 2>/dev/null || echo '') --tail=100 || echo 'No qdrant pods found'", "backup:postgres": "node scripts/backup-postgres.js", "check:qdrant": "kubectl exec -n tenk8n $(kubectl get pods -n tenk8n -l app=qdrant ) -- curl -s http://localhost:6333/readyz"}, "author": "The Handsomest Nerd, Inc.", "license": "UNLICENSED", "dependencies": {"dotenv": "^16.0.3", "moment": "^2.29.4"}}