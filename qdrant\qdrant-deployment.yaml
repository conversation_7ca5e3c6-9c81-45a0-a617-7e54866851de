apiVersion: apps/v1
kind: Deployment
metadata:
  name: qdrant
  namespace: tenk8n
  labels:
    app: qdrant
spec:
  replicas: 1
  selector:
    matchLabels:
      app: qdrant
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: qdrant
    spec:
      # Add tolerations for all common GKE taints
      tolerations:
      - key: "cloud.google.com/gke-preemptible"
        operator: "Exists"
        effect: "NoSchedule"
      - key: "cloud.google.com/gke-spot"
        operator: "Exists"
        effect: "NoSchedule"
      - key: "cloud.google.com/gke-quick-remove"
        operator: "Exists"
        effect: "NoSchedule"
      # Make node affinity less restrictive - use preferredDuringScheduling instead of required
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: topology.kubernetes.io/zone
                operator: In
                values:
                - us-central1-a
                - us-central1-b
                - us-central1-c
                - us-central1-f
      containers:
      - name: qdrant
        image: qdrant/qdrant:latest
        ports:
        - containerPort: 6333
          name: http
        - containerPort: 6334
          name: grpc
        env:
        - name: QDRANT__SERVICE__API_KEY
          valueFrom:
            secretKeyRef:
              name: qdrant-secret
              key: api-key
        # # - name: QDRANT__SERVICE__READ_ONLY_API_KEY
        ##    valueFrom:
        # #     secretKeyRef:
        # #       name: qdrant-secret
        # #       key: read-only-api-key
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        volumeMounts:
        - name: qdrant-storage
          mountPath: /qdrant/storage
        - name: qdrant-snapshots
          mountPath: /qdrant/snapshots
        - name: qdrant-config
          mountPath: /qdrant/config
          readOnly: true
        livenessProbe:
          httpGet:
            path: /readyz
            port: 6333
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /readyz
            port: 6333
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: qdrant-storage
        persistentVolumeClaim:
          claimName: qdrant-storage
      - name: qdrant-snapshots
        emptyDir: {}
      - name: qdrant-config
        configMap:
          name: qdrant-config




