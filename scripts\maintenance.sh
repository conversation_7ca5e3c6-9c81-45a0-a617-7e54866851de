#!/bin/bash
set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ID=$(gcloud config get-value project)
REGION=$(gcloud config get-value compute/region)
NAMESPACE="tenk8n"
CLUSTER_NAME="tenk8n"

# Helper functions
function print_header() {
  echo -e "\n${GREEN}==== $1 ====${NC}\n"
}

function check_command() {
  if ! command -v $1 &> /dev/null; then
    echo -e "${RED}Error: $1 is not installed${NC}"
    exit 1
  fi
}

# Check required tools
check_command kubectl
check_command gcloud

# Connect to cluster
function connect_cluster() {
  print_header "Connecting to GKE cluster"
  gcloud container clusters get-credentials $CLUSTER_NAME --region $REGION --project $PROJECT_ID
}

# Check pod status
function check_pods() {
  print_header "Checking pod status"
  kubectl get pods -n $NAMESPACE
}

# Check persistent volumes
function check_volumes() {
  print_header "Checking persistent volumes"
  kubectl get pv
  print_header "Checking persistent volume claims"
  kubectl get pvc -n $NAMESPACE
}

# Restart deployments
function restart_deployment() {
  if [ -z "$1" ]; then
    echo -e "${RED}Error: Deployment name required${NC}"
    return 1
  fi
  
  print_header "Restarting $1 deployment"
  kubectl rollout restart deployment/$1 -n $NAMESPACE
  kubectl rollout status deployment/$1 -n $NAMESPACE
}

# Backup database
function backup_postgres() {
  print_header "Creating PostgreSQL backup"
  POD_NAME=$(kubectl get pods -n $NAMESPACE -l service=postgres-n8n -o jsonpath="{items[0].metadata.name}")
  TIMESTAMP=$(date +%Y%m%d_%H%M%S)
  
  echo "Using pod: $POD_NAME"
  kubectl exec -n $NAMESPACE $POD_NAME -- pg_dump -U tenk8nDB n8n > ./backups/n8n_backup_$TIMESTAMP.sql
  echo -e "${GREEN}Backup saved to ./backups/n8n_backup_$TIMESTAMP.sql${NC}"
}

# Check Qdrant status
function check_qdrant() {
  print_header "Checking Qdrant status"
  
  # First check if any qdrant pods exist
  POD_COUNT=$(kubectl get pods -n $NAMESPACE -l app=qdrant --no-headers | wc -l)
  
  if [ "$POD_COUNT" -eq 0 ]; then
    echo -e "${RED}No Qdrant pods found${NC}"
    echo "Available pods:"
    kubectl get pods -n $NAMESPACE
    return 1
  fi
  
  POD_NAME=$(kubectl get pods -n $NAMESPACE -l app=qdrant -o jsonpath="{.items[0].metadata.name}")
  
  echo "Using pod: $POD_NAME"
  kubectl exec -n $NAMESPACE $POD_NAME -- curl -s http://localhost:6333/readyz
  echo ""
  kubectl exec -n $NAMESPACE $POD_NAME -- curl -s http://localhost:6333/metrics | head -n 20
}

# View logs
function view_logs() {
  if [ -z "$1" ]; then
    echo -e "${RED}Error: Deployment name required${NC}"
    return 1
  fi
  
  print_header "Viewing logs for $1"
  
  # First check if any pods exist with this label
  POD_COUNT=$(kubectl get pods -n $NAMESPACE -l app=$1 --no-headers | wc -l)
  
  if [ "$POD_COUNT" -gt 0 ]; then
    # Try with app label first
    POD_NAME=$(kubectl get pods -n $NAMESPACE -l app=$1 -o jsonpath="{.items[0].metadata.name}" 2>/dev/null)
  else
    # Try with service label
    POD_COUNT=$(kubectl get pods -n $NAMESPACE -l service=$1 --no-headers | wc -l)
    if [ "$POD_COUNT" -gt 0 ]; then
      POD_NAME=$(kubectl get pods -n $NAMESPACE -l service=$1 -o jsonpath="{.items[0].metadata.name}" 2>/dev/null)
    else
      # If both failed, try a more generic approach
      POD_NAME=$(kubectl get pods -n $NAMESPACE | grep $1 | head -n 1 | awk '{print $1}')
    fi
  fi
  
  if [ -z "$POD_NAME" ]; then
    echo -e "${RED}No pods found for $1${NC}"
    echo "Available pods:"
    kubectl get pods -n $NAMESPACE
    return 1
  fi
  
  echo "Using pod: $POD_NAME"
  kubectl logs -n $NAMESPACE $POD_NAME --tail=100
}

# Main menu
function show_menu() {
  echo -e "${GREEN}=== Maintenance Operations ===${NC}"
  echo "1) Connect to cluster"
  echo "2) Check pod status"
  echo "3) Check volumes"
  echo "4) Restart n8n"
  echo "5) Restart PostgreSQL"
  echo "6) Restart Qdrant"
  echo "7) Backup PostgreSQL database"
  echo "8) Check Qdrant status"
  echo "9) View n8n logs"
  echo "10) View PostgreSQL logs"
  echo "11) View Qdrant logs"
  echo "q) Quit"
  echo ""
  read -p "Select an option: " option
  
  case $option in
    1) connect_cluster ;;
    2) check_pods ;;
    3) check_volumes ;;
    4) restart_deployment "n8n" ;;
    5) restart_deployment "postgres" ;;
    6) restart_deployment "qdrant" ;;
    7) backup_postgres ;;
    8) check_qdrant ;;
    9) view_logs "n8n" ;;
    10) view_logs "postgres-n8n" ;;
    11) view_logs "qdrant" ;;
    q) exit 0 ;;
    *) echo -e "${RED}Invalid option${NC}" ;;
  esac
  
  read -p "Press Enter to continue..."
  show_menu
}

# Create backups directory if it doesn't exist
mkdir -p ./backups

# Start the menu
show_menu
